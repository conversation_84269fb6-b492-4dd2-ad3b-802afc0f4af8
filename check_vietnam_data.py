import geopandas as gpd
import pandas as pd

# Đọc file shapefile Vietnam districts level 2
try:
    gdf = gpd.read_file("vietnam_Districts_level_2/vietnam_Districts_level_2.shp")
    
    print("Thông tin cơ bản về dữ liệu:")
    print(f"Số lượng districts: {len(gdf)}")
    print(f"Các cột có sẵn: {list(gdf.columns)}")
    print(f"CRS: {gdf.crs}")
    
    print("\nMẫu dữ liệu (5 dòng đầu):")
    print(gdf.head())
    
    print("\nCác giá trị unique trong các cột chính:")
    for col in gdf.columns:
        if col != 'geometry':
            unique_count = gdf[col].nunique()
            print(f"{col}: {unique_count} giá trị unique")
            if unique_count < 20:  # Chỉ hiển thị nếu ít hơn 20 giá trị
                print(f"  Các giá trị: {sorted(gdf[col].unique())}")
    
    print("\nKiểm tra dữ liệu Hà Nội:")
    # Tìm các district có tên chứa "Hanoi" hoặc "Ha Noi" trong shape2 hoặc shape1
    hanoi_districts_shape2 = gdf[gdf['shape2'].str.contains('Hanoi|Ha Noi', case=False, na=False)]
    hanoi_districts_shape1 = gdf[gdf['shape1'].str.contains('Hanoi|Ha Noi', case=False, na=False)]

    print(f"Số districts có tên chứa Hanoi trong shape2: {len(hanoi_districts_shape2)}")
    print(f"Số districts có tên chứa Hanoi trong shape1: {len(hanoi_districts_shape1)}")

    if len(hanoi_districts_shape2) > 0:
        print("Danh sách districts Hà Nội (shape2):")
        for idx, row in hanoi_districts_shape2.iterrows():
            print(f"  {row['shape2']} - {row['shape1']}")

    if len(hanoi_districts_shape1) > 0:
        print("Danh sách districts Hà Nội (shape1):")
        for idx, row in hanoi_districts_shape1.iterrows():
            print(f"  {row['shape2']} - {row['shape1']}")

    # Kiểm tra tất cả các tỉnh/thành phố
    print(f"\nTất cả các tỉnh/thành phố (shape1): {sorted(gdf['shape1'].unique())}")
    print(f"\nTất cả các quận/huyện (shape2): {sorted(gdf['shape2'].unique())}")
            
except Exception as e:
    print(f"Lỗi khi đọc file: {e}")
