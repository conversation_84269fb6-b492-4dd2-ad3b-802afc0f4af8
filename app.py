import geopandas as gpd
import matplotlib.pyplot as plt
import pandas as pd
import tkinter as tk
from tkinter import ttk, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.colors as colors
import numpy as np
from shapely.geometry import Polygon
import sys
import os

def resource_path(relative_path):
    base_path = getattr(sys, '_MEIPASS', os.path.abspath(".")) if getattr(sys, 'frozen', False) else os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class HanoiRainfallMapApp:
    def __init__(self, root):
        self.root = root
        self.root.title("<PERSON><PERSON><PERSON> đồ l<PERSON>ợng m<PERSON> Nộ<PERSON>")
        self.root.geometry("1000x700")
        
        # <PERSON>h s<PERSON>ch quận/huy<PERSON>n H<PERSON> Nội
        self.districts = [
            "Ba Vì", "<PERSON>ơn Tây", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
            "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>m",
            "<PERSON><PERSON>u <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON> <PERSON><PERSON>nh", "<PERSON>n <PERSON>m", "<PERSON> <PERSON> <PERSON>r<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>",
            "<PERSON><PERSON>", "<PERSON>h <PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>m", "<PERSON> <PERSON><PERSON><PERSON>n", "<PERSON>h <PERSON>r<PERSON>",
            "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON>h <PERSON><PERSON>", "<PERSON>h<PERSON>ờng Tín", "Phú Xuyên", "Mỹ Đức", "Ứng Hòa"
        ]
        
        # Quận/huyện không hiển thị tên
        self.excluded_districts = ["Ba Đình", "Hoàn Kiếm", "Đống Đa", "Hai Bà Trưng", 
                                  "Thanh Xuân", "Nam Từ Liêm", "Cầu Giấy", "Tây Hồ", 
                                  "Bắc Từ Liêm", "Long Biên"]
        
        # Tải dữ liệu bản đồ
        self.load_map_data()
        
        # Khởi tạo giao diện
        self.create_widgets()
        self.update_map()
    
    def load_map_data(self):
        try:
            shapefile_path = resource_path("BoundariesMap/HanoiBoundariesMap.shp")
            self.gdf = gpd.read_file(shapefile_path)
            self.district_column = 'NAME_2'
            
            if not all(district in self.gdf[self.district_column].values for district in self.districts):
                raise ValueError("Danh sách quận không khớp với shapefile.")
                
        except Exception as e:
            messagebox.showinfo("Thông báo", f"Lỗi: {str(e)}\nSử dụng dữ liệu mẫu.")
            self.create_sample_data()
            
        # Khởi tạo dữ liệu nhiệt độ
        self.temp_data = {district: 0 for district in self.districts}
        self.gdf['rainfall'] = self.gdf[self.district_column].map(self.temp_data)
        self.colorbar = None
    
    def create_sample_data(self):
        # Tạo dữ liệu mẫu khi không có shapefile
        polygons = []
        x_coords = np.random.uniform(105.5, 106.0, len(self.districts))
        y_coords = np.random.uniform(20.8, 21.2, len(self.districts))
        
        for i, _ in enumerate(self.districts):
            # Tạo đa giác ngẫu nhiên
            center_x, center_y = x_coords[i], y_coords[i]
            points = [(center_x + 0.03 * np.cos(j * np.pi/3) * (0.8 + 0.4 * np.random.random()), 
                      center_y + 0.03 * np.sin(j * np.pi/3) * (0.8 + 0.4 * np.random.random())) 
                     for j in range(6)]
            polygons.append(Polygon(points))
        
        self.district_column = 'district'
        self.gdf = gpd.GeoDataFrame({self.district_column: self.districts, 'geometry': polygons})
        self.gdf.crs = "EPSG:4326"
    
    def create_widgets(self):
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Frame cho input
        self.create_input_frame(main_frame)
        
        # Frame cho bản đồ
        map_frame = ttk.Frame(main_frame)
        map_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5), pady=5)
        
        # Tạo figure với kích thước cố định
        self.fig, self.ax = plt.subplots(figsize=(8, 8))
        self.canvas = FigureCanvasTkAgg(self.fig, master=map_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def create_input_frame(self, parent):
        input_frame = ttk.Frame(parent, padding=5, width=250)
        input_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        ttk.Label(input_frame, text="Nhập nhiệt độ các quận/huyện", font=("Arial", 12, "bold")).pack(pady=5)
        
        # Tạo khung cuộn
        entry_canvas = tk.Canvas(input_frame, width=230)
        scrollbar = ttk.Scrollbar(input_frame, orient="vertical", command=entry_canvas.yview)
        scrollable_frame = ttk.Frame(entry_canvas)
        
        scrollable_frame.bind("<Configure>", lambda e: entry_canvas.configure(scrollregion=entry_canvas.bbox("all")))
        entry_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        entry_canvas.configure(yscrollcommand=scrollbar.set)
        
        entry_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Tạo entry boxes
        self.entries = {}
        for district in self.districts:
            frame = ttk.Frame(scrollable_frame)
            frame.pack(pady=2, fill=tk.X)
            
            ttk.Label(frame, text=f"{district}:", font=("Arial", 10)).pack(side=tk.LEFT, padx=2)
            entry = ttk.Entry(frame, width=10, font=("Arial", 10))
            entry.pack(side=tk.RIGHT, padx=5)
            entry.insert(0, str(self.temp_data[district]))
            self.entries[district] = entry
        
        # Frame cho ngày và tháng
        date_frame = ttk.Frame(scrollable_frame)
        date_frame.pack(pady=5, fill=tk.X)
        
        ttk.Label(date_frame, text="Từ:", font=("Arial", 10)).pack(side=tk.LEFT, padx=2)
        self.day_entry = ttk.Entry(date_frame, width=5, font=("Arial", 10))
        self.day_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(date_frame, text="Đến:", font=("Arial", 10)).pack(side=tk.LEFT, padx=2)
        self.month_entry = ttk.Entry(date_frame, width=5, font=("Arial", 10))
        self.month_entry.pack(side=tk.LEFT, padx=2)
        
        # Nút cập nhật
        style = ttk.Style()
        style.configure("Custom.TButton", font=("Arial", 12))
        ttk.Button(input_frame, text="Cập nhật bản đồ", command=self.update_temperatures, 
                  width=20, style="Custom.TButton").pack(side=tk.BOTTOM, pady=10)
    
    def update_temperatures(self):
        for district, entry in self.entries.items():
            try:
                self.temp_data[district] = float(entry.get())
            except ValueError:
                pass
        
        self.gdf['rainfall'] = self.gdf[self.district_column].map(self.temp_data)
        self.update_map()
    
    def update_map(self):
        self.ax.clear()
        
        # Cấu hình màu sắc và ranh giới
        color_list = [
            '#d96073', '#b84557', '#9c2538', '#c21d27', '#dc3c2d', '#ee623e', '#f98b50',
            '#fdb264', '#fcd383', '#ffeaa1', '#fffebe', '#ffffff', '#ffffff',
            '#9ef8f5', '#8ce3f6', '#78ccf6', '#65b7f9', '#3a89de', '#2975c6',
            '#125fad', '#014692', '#254aa6', '#484eb9', '#6d53ca'
        ]
        
        boundaries = [-5.0, -4.5, -4.0, -3.5, -3.0, -2.5, -2.0, -1.5, -1.0, -0.75, -0.5, -0.25, 
                     0.0, 0.25, 0.5, 0.75, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0]
        
        # Tạo colormap và vẽ bản đồ
        cmap = colors.ListedColormap(color_list[::-1])
        norm = colors.BoundaryNorm(boundaries, cmap.N)
        
        self.gdf.plot(column='rainfall', ax=self.ax, cmap=cmap, norm=norm, 
                     edgecolor='black', linewidth=0.5, legend=False)
        
        # Tạo colorbar với chiều rộng thu nhỏ
        if self.colorbar is not None:
            self.colorbar.remove()
        
        sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
        self.colorbar = self.fig.colorbar(sm, ax=self.ax, orientation='vertical', 
                                         fraction=0.02, pad=0.01, aspect=40, shrink=0.6)
        self.colorbar.set_label('Chuẩn sai nhiệt độ (°C)', fontsize=9)
        self.colorbar.set_ticks(boundaries)
        self.colorbar.set_ticklabels([str(b) for b in boundaries])
        self.colorbar.ax.tick_params(labelsize=7)
        self.colorbar.ax.yaxis.set_ticks_position('right')
        
        # Hiển thị tên quận
        for idx, row in self.gdf.iterrows():
            district_name = row[self.district_column]
            if district_name not in self.excluded_districts:
                x, y = row.geometry.centroid.x, row.geometry.centroid.y
                self.ax.text(x, y, district_name, fontsize=8, ha='center', va='center',
                            bbox=dict(facecolor='white', alpha=0.5, edgecolor='none'))
        
        # Cập nhật tiêu đề
        day = self.day_entry.get().strip()
        month = self.month_entry.get().strip()
        self.ax.set_title(f"Bản đồ dự báo nhiệt độ từ {day} đến {month}", fontsize=14)
        
        # Cấu hình hiển thị
        self.ax.set_aspect('equal', adjustable='box')
        self.ax.set_axis_off()
        self.fig.subplots_adjust(left=0.05, right=0.95, top=0.95, bottom=0.05)
        
        # Cập nhật canvas
        self.canvas.draw()

def main():
    root = tk.Tk()
    app = HanoiRainfallMapApp(root)
    
    # Xử lý sự kiện đóng cửa sổ
    def on_closing():
        plt.close('all')  # Đóng tất cả các figure của matplotlib
        root.destroy()    # Hủy cửa sổ Tkinter
        
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
